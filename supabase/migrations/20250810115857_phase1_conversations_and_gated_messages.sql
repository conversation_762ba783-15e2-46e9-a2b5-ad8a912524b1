-- Phase 1: Conversations & connection-gated messaging
-- Adds: conversations, conversation_participants, conversation_id on messages
-- RLS: only participants can read/write; insert only if accepted connection exists
-- RPC: get_or_create_conversation(partner_id)

-- Safety: make sure required extensions exist
create extension if not exists pgcrypto;

-- 1) Conversations and participants
create table if not exists public.conversations (
  id uuid primary key default gen_random_uuid(),
  created_at timestamptz not null default now()
);

create table if not exists public.conversation_participants (
  conversation_id uuid not null references public.conversations(id) on delete cascade,
  user_id uuid not null,
  created_at timestamptz not null default now(),
  primary key (conversation_id, user_id)
);

create index if not exists idx_conversation_participants_conversation on public.conversation_participants(conversation_id);
create index if not exists idx_conversation_participants_user on public.conversation_participants(user_id);

-- 2) Messages: add conversation_id and index (keep sender_id/receiver_id for compatibility)
alter table public.messages
  add column if not exists conversation_id uuid references public.conversations(id) on delete cascade;

create index if not exists idx_messages_conversation on public.messages(conversation_id);

-- 3) RLS: enable and tighten access
alter table public.conversations enable row level security;
alter table public.conversation_participants enable row level security;

-- Conversations: only participants can select; block direct insert (use function)
create policy if not exists conv_select_if_participant on public.conversations
for select using (
  exists (
    select 1 from public.conversation_participants cp
    where cp.conversation_id = conversations.id and cp.user_id = auth.uid()
  )
);

create policy if not exists conv_insert_none on public.conversations
for insert with check (false);

-- Participants: user can see their own rows; block direct inserts (use function)
create policy if not exists cp_select_own on public.conversation_participants
for select using (user_id = auth.uid());

create policy if not exists cp_insert_none on public.conversation_participants
for insert with check (false);

-- Messages: replace permissive policies with connection-gated policies
-- Drop existing simple policies if they exist (names from pulled schema)
drop policy if exists "Users can send messages" on public.messages;
drop policy if exists "Users can view their messages" on public.messages;
-- Keep or replace update policy; we re-add with participant check below
drop policy if exists "Users can update received messages" on public.messages;

-- Select: only participants in the conversation can view messages
create policy msg_select_if_participant on public.messages
for select using (
  exists (
    select 1 from public.conversation_participants cp
    where cp.conversation_id = messages.conversation_id and cp.user_id = auth.uid()
  )
);

-- Insert: sender must be participant AND there must be an accepted connection between participants
create policy msg_insert_if_participant_and_connected on public.messages
for insert with check (
  sender_id = auth.uid()
  and conversation_id is not null
  and exists (
    select 1 from public.conversation_participants cp_self
    where cp_self.conversation_id = messages.conversation_id
      and cp_self.user_id = auth.uid()
  )
  and exists (
    select 1
    from public.conversation_participants cp_self
    join public.conversation_participants cp_other
      on cp_other.conversation_id = cp_self.conversation_id
     and cp_other.user_id <> cp_self.user_id
    join public.connections c
      on (
        (c.requester_id = cp_self.user_id and c.receiver_id = cp_other.user_id) or
        (c.requester_id = cp_other.user_id and c.receiver_id = cp_self.user_id)
      )
    where cp_self.conversation_id = messages.conversation_id
      and cp_self.user_id = auth.uid()
      and c.status = 'accepted'
  )
);

-- Update: allow participants to update their own received messages (e.g., read receipts)
create policy msg_update_if_participant_receiver on public.messages
for update using (
  exists (
    select 1 from public.conversation_participants cp
    where cp.conversation_id = messages.conversation_id and cp.user_id = auth.uid()
  ) and receiver_id = auth.uid()
);

-- 4) Trigger to populate receiver_id based on conversation participants when not provided
create or replace function public.set_message_receiver_from_conversation()
returns trigger
language plpgsql
security definer
set search_path = public
as $$
declare
  other_user uuid;
begin
  if NEW.conversation_id is null then
    raise exception 'conversation_id is required';
  end if;

  if NEW.sender_id is null then
    NEW.sender_id := auth.uid();
  end if;

  select cp.user_id
    into other_user
  from public.conversation_participants cp
  where cp.conversation_id = NEW.conversation_id
    and cp.user_id <> NEW.sender_id
  limit 1;

  if other_user is null then
    raise exception 'Could not resolve receiver for conversation %', NEW.conversation_id;
  end if;

  if NEW.receiver_id is null then
    NEW.receiver_id := other_user;
  end if;

  return NEW;
end;
$$;

-- Attach trigger to messages inserts
create or replace trigger trg_messages_set_receiver
before insert on public.messages
for each row execute function public.set_message_receiver_from_conversation();

-- 5) get_or_create_conversation RPC: only works when users are connected
create or replace function public.get_or_create_conversation(partner_id uuid)
returns uuid
language plpgsql
security definer
set search_path = public
as $$
declare
  me uuid := auth.uid();
  conv_id uuid;
begin
  if me is null then
    raise exception 'Not authenticated';
  end if;

  -- Ensure accepted connection exists
  if not exists (
    select 1 from public.connections c
    where c.status = 'accepted'
      and (
        (c.requester_id = me and c.receiver_id = partner_id) or
        (c.requester_id = partner_id and c.receiver_id = me)
      )
  ) then
    raise exception 'No accepted connection between users';
  end if;

  -- Try to find existing conversation for these two users
  select c.id into conv_id
  from public.conversations c
  where exists (
          select 1 from public.conversation_participants p
          where p.conversation_id = c.id and p.user_id = me
        )
    and exists (
          select 1 from public.conversation_participants p
          where p.conversation_id = c.id and p.user_id = partner_id
        )
  limit 1;

  if conv_id is not null then
    return conv_id;
  end if;

  -- Create new conversation and add both participants
  insert into public.conversations default values returning id into conv_id;
  insert into public.conversation_participants (conversation_id, user_id)
  values (conv_id, me), (conv_id, partner_id);

  return conv_id;
end;
$$;