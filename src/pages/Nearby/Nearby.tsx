import './Nearby.css';
import { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Heart, MessageCircle, Clock, ArrowLeft, Users, Search } from 'lucide-react';
import MobileNavigation from '@/components/MobileNavigation';
import DebugLocationInfo from '@/components/DebugLocationInfo';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { GoogleMap, Marker, useLoadScript, OverlayView, Circle, InfoWindow } from '@react-google-maps/api';
import { MarkerClusterer } from '@react-google-maps/api';
import turfDistance from '@turf/distance';
import { getUserLocation } from '@/hooks/useUserLocation';

const mapContainerStyle = { width: '100vw', height: '50vh' };

const customMapStyle = [
  // Hide roads (keep minimal map styling)
  { featureType: "road", elementType: "geometry", stylers: [{ visibility: "on" }] },
  { featureType: "road", elementType: "labels", stylers: [{ visibility: "on" }] },
  // Show businesses and POI icons
  { featureType: "poi.business", stylers: [{ visibility: "off" }] },
  // Keep transit hidden
  { featureType: "transit", stylers: [{ visibility: "off" }] },
  // Show place names and icons
  { featureType: "poi", elementType: "labels.text", stylers: [{ visibility: "off" }] },
  { featureType: "poi", elementType: "labels.icon", stylers: [{ visibility: "off" }] },
  { featureType: "administrative", elementType: "labels.text", stylers: [{ visibility: "on" }] },
];

const defaultCenter = {
  lat: 28.6139, // Delhi as fallback
  lng: 77.2090
};


interface Post {
  id: string;
  content: string;
  post_type: string;
  created_at: string;
  likes_count: number;
  comments_count: number;
  business_name: string | null;
  event_date: string | null;
  event_location: string | null;
  deal_expires_at: string | null;
  profiles: {
    full_name: string;
    username: string;
  };
}

type NearbyUser = {
  user_id: string;
  lat: number;
  lng: number;
  distance: number;
  full_name: string;
  username: string;
  bio: string;
  interests: string[];
  is_business: boolean;
  avatar_url?: string; // Optional avatar URL
};

type NearbyUserResult = {
  id: string;
  user_id: string;
  full_name: string;
  username: string;
  bio: string;
  interests: string[];
  is_business: boolean;
  lat: number;
  lng: number;
  distance_m: number;
  avatar_url?: string; // Optional avatar URL
};


const Nearby = () => {
  // Track if initial location has been set to prevent effect from running after manual updates
  const initialLocationSetRef = useRef(false);
  const navigate = useNavigate();
  const { user, loading } = useAuth();
  const [posts, setPosts] = useState<Post[]>([]);
  const [loadingPosts, setLoadingPosts] = useState(true);
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [nearbyUsers, setNearbyUsers] = useState<NearbyUser[]>([]);
  const [loadingNearby, setLoadingNearby] = useState(false);
  const DEFAULT_RADIUS = 1000; // meters
  const [radius, setRadius] = useState(DEFAULT_RADIUS);
  const [selectedUser, setSelectedUser] = useState<NearbyUser | null>(null);
  // Remove interestFilter, add tagSearch for searching tags/interests
  const [tagSearch, setTagSearch] = useState('');
  const [accuracy, setAccuracy] = useState(30); // default 30m
  const [mapZoom, setMapZoom] = useState(15);
  const [mapKey, setMapKey] = useState(0);


  const { isLoaded: mapsLoaded } = useLoadScript({
    googleMapsApiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
  });

  const fetchNearbyPosts = async () => {
    try {
      const { data: postsData, error } = await supabase
        .from('posts')
        .select(`
          id,
          content,
          post_type,
          created_at,
          likes_count,
          comments_count,
          business_name,
          event_date,
          event_location,
          deal_expires_at,
          user_id
        `)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        console.error('Error fetching posts:', error);
        toast({
          title: "Error",
          description: "Failed to load nearby posts",
          variant: "destructive",
        });
        return;
      }

      // Fetch user profiles for the posts
      const userIds = postsData?.map(post => post.user_id) || [];
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('user_id, full_name, username')
        .in('user_id', userIds);

      if (profileError) {
        console.error('Error fetching profiles:', profileError);
      }

      // Combine posts with profile data
      const postsWithProfiles = postsData?.map(post => ({
        ...post,
        profiles: profiles?.find(p => p.user_id === post.user_id) || {
          full_name: 'Unknown User',
          username: 'unknown'
        }
      })) || [];

      setPosts(postsWithProfiles);
    } catch (error) {
      console.error('Error fetching posts:', error);
    } finally {
      setLoadingPosts(false);
    }
  };

  // Upsert user location in Supabase
  const upsertUserLocation = useCallback(async (lat: number, lng: number) => {
    if (!user) return;
    console.log(`📍 Updating user location to [${lat}, ${lng}]`);

    // Get current profile to check visibility status
    const { data: profile } = await supabase
      .from('profiles')
      .select('is_visible, visibility_radius')
      .eq('user_id', user.id)
      .single();

    const { error } = await supabase.from('profiles').update({
      location_lat: lat,
      location_lng: lng,
      // Ensure visibility is enabled when updating location
      is_visible: profile?.is_visible ?? true,
      // Use existing visibility radius or default to 5000m
      visibility_radius: profile?.visibility_radius ?? 5000,
      updated_at: new Date().toISOString(),
    }).eq('user_id', user.id);

    if (error) {
      console.error('❌ Failed to update location in database:', error);
      return false;
    }

    console.log('✅ Location updated in database');
    return true;
  }, [user]);

  // Fetch nearby users from Supabase
  const fetchNearbyUsers = useCallback(async (lat: number, lng: number) => {
    setLoadingNearby(true);
    console.log(`🔍 Searching for nearby users at [${lat}, ${lng}] with radius ${radius/1000}km`);

    // First, ensure our own location is properly saved
    try {
      await upsertUserLocation(lat, lng);
      console.log('✅ Updated own location in database');
    } catch (err) {
      console.error('❌ Failed to update own location:', err);
    }

    // Then search for nearby users
    const { data, error } = await supabase.rpc('find_nearby_users', {
      user_lat: lat,
      user_lng: lng,
  radius_m: 5000, // Increased to 5km for better discovery
    });

    if (error) {
      console.error('❌ Error finding nearby users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load nearby users: ' + error.message,
        variant: 'destructive'
      });
    } else {
      console.log(`✅ Found ${data?.length || 0} nearby users`);
      if (data?.length) {
        console.log('📍 First nearby user:', data[0]);
      }

      setNearbyUsers(
        (data as NearbyUserResult[] || []).map((u) => ({
          user_id: u.user_id,
          lat: u.lat ?? 0,
          lng: u.lng ?? 0,
          distance: u.distance_m ?? 0,
          full_name: u.full_name ?? '',
          username: u.username ?? '',
          bio: u.bio ?? '',
          interests: u.interests ?? [],
          is_business: u.is_business ?? false,
          avatar_url: u.avatar_url ?? '/icons/user-avatar.svg',
        }))
      );
    }
    setLoadingNearby(false);
  }, [radius]);

  const getPostTypeColor = (type: string) => {
    switch (type) {
      case 'event':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'deal':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  // Calculate distance between two lat/lng points (Haversine via turf)
  const isWithinRadius = (lat1, lng1, lat2, lng2, r) => {
    const from = [lng1, lat1];
    const to = [lng2, lat2];
    const dist = turfDistance(from, to, { units: 'meters' });
    return dist <= r;
  };

  // Filter users by tag search (case-insensitive, partial match)
  const normalizedTagSearch = tagSearch.trim().toLowerCase();
  const filteredNearbyUsers = !normalizedTagSearch
    ? nearbyUsers
    : nearbyUsers.filter(u =>
        (u.interests || []).some(i => i.trim().toLowerCase().includes(normalizedTagSearch))
      );

  // Make map center and radius configurable
  const [mapCenter, setMapCenter] = useState(defaultCenter);

  // Dynamic interest options from nearby users
  const allInterests = Array.from(new Set(nearbyUsers.flatMap(u => u.interests || [])));

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  // Manual location fetch handler
  const handleLocateMe = async () => {
    const loc = await getUserLocation();
    if (loc) {
      setUserLocation(loc);
      setMapZoom(17); // Reset zoom on new location
      setMapKey(prevKey => prevKey + 1); // Update map key to trigger re-render
      await upsertUserLocation(loc.lat, loc.lng);
      fetchNearbyUsers(loc.lat, loc.lng);
      setAccuracy(30); // or use actual accuracy if available
      toast({ title: 'Location Updated', description: 'Your location has been updated.', variant: 'default' });
    } else {
      toast({ title: 'Location Error', description: 'Unable to fetch your location', variant: 'destructive' });
    }
  };

  // On first visit, automatically locate user (runs only once after mount)
  useEffect(() => {
    if (user && !userLocation && !initialLocationSetRef.current) {
      (async () => {
        const loc = await getUserLocation();
        if (loc) {
          setUserLocation(loc);
          await upsertUserLocation(loc.lat, loc.lng);
          fetchNearbyUsers(loc.lat, loc.lng);
          initialLocationSetRef.current = true;
        } else {
          toast({ title: 'Location Error', description: 'Unable to fetch your location', variant: 'destructive' });
        }
      })();
    }
    // Only run once after mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset initialLocationSetRef and location when user logs out
  useEffect(() => {
    if (!user) {
      initialLocationSetRef.current = false;
      setUserLocation(null);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      fetchNearbyPosts();
    }
  }, [user]);

  const circleRef = useRef<google.maps.Circle | null>(null);
  const listenersRef = useRef<google.maps.MapsEventListener[]>([]);
  // Attach listeners only once when the circle loads
  const handleCircleLoad = (circle) => {
    circleRef.current = circle;
    listenersRef.current.forEach(listener => listener.remove());
    listenersRef.current = [];
    listenersRef.current.push(
      circle.addListener('radius_changed', () => {
        try {
          const r = circle.getRadius();
          if (typeof r === 'number' && !isNaN(r)) {
            setRadius(r);
          }
        } catch (err) {
          toast({ title: 'Map Error', description: 'Failed to update radius', variant: 'destructive' });
        }
      })
    );
    // center_changed listener removed: location only updates on button click
  };

  // Helper: send connection request
  const sendConnectionRequest = useCallback(async (receiverId: string) => {
    if (!user) {
      toast({ title: 'Sign in required', description: 'Please sign in to connect with users.', variant: 'destructive' });
      navigate('/auth');
      return;
    }
    try {
      const { error } = await supabase.from('connections').insert({
        requester_id: user.id,
        receiver_id: receiverId,
        status: 'pending',
      });
      if (error) throw error;
      toast({ title: 'Request Sent', description: 'Connection request sent.' });
    } catch (e) {
      console.error('Send connection request failed:', e);
      toast({ title: 'Error', description: 'Could not send connection request.', variant: 'destructive' });
    }
  }, [user, navigate]);

  const handleMessage = useCallback(async (partnerId: string) => {
    if (!user) {
      toast({ title: 'Sign in required', description: 'Please sign in to message users.', variant: 'destructive' });
      navigate('/auth');
      return;
    }
    try {
      const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
      if (error) throw error;
      navigate(`/messages?c=${data}&u=${partnerId}`);
    } catch (e) {
      // Most likely not connected yet
      toast({ title: 'Connect first', description: 'Send a connection request before messaging.', variant: 'destructive' });
    }
  }, [user, navigate]);

  // Add debug logging to help diagnose map rendering issues
  useEffect(() => {
    console.log('mapsLoaded:', mapsLoaded, 'userLocation:', userLocation);
  }, [mapsLoaded, userLocation]);

  return (
    <div className="nearby-root-container min-h-screen bg-background pb-20 mobile-safe-area">
      {/* Header */}
      <header className="nearby-header bg-card border-b border-border sticky top-0 z-40">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => navigate('/')}
                className="md:hidden"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <MapPin className="h-6 w-6 text-primary" />
              <h1 className="text-l font-bold">Nearby Posts</h1>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{posts.length} posts</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="nearby-main">
        {/* Google Map for Nearby Users */}
        {mapsLoaded && userLocation ? (
          <div className="nearby-map rounded-lg overflow-hidden border border-border relative">
            {/* Filter UI */}
            <div className="absolute top-15 right-0 z-20 bg-white/90 rounded shadow p-2 flex gap-2 justify-between items-center">
              <input
                id="tag-search"
                type="text"
                value={tagSearch}
                onChange={e => setTagSearch(e.target.value)}
                placeholder="Type to search interests..."
                className="w-[220px] border rounded px-2 py-1 focus:ring-primary"
              />
              {/* Looking glass icon */}
              <Search
                className="h-4 w-4 text-muted-foreground"
                onClick={() => {
                  if (tagSearch.trim()) {
                    toast({
                      title: 'Search',
                      description: `Searching for people interested in "${tagSearch}"`,
                    });
                  }
                }}
              />
            </div>
            <div className="absolute bottom-6 right-3 z-30">
                <Button onClick={handleLocateMe} variant="outline" className="locate-me-button shadow-lg">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="7"></circle>
                    <line x1="12" y1="1" x2="12" y2="4"></line>
                    <line x1="12" y1="20" x2="12" y2="23"></line>
                    <line x1="1" y1="12" x2="4" y2="12"></line>
                    <line x1="20" y1="12" x2="23" y2="12"></line>
                  </svg>
                </Button>
            </div>
            <GoogleMap
              key={mapKey} // Use key to force re-render on location change
              mapContainerStyle={mapContainerStyle}
              zoom={mapZoom}
              center={userLocation}
              options={{
                styles: customMapStyle,
                disableDefaultUI: true,
                zoomControl: false,
                streetViewControl: false,
                mapTypeControl: false,
                clickableIcons: false,
              }}
            >
              {/* Accuracy circle (uses browser geolocation accuracy) */}
              <Circle
                center={userLocation}
                radius={accuracy}
                options={{
                  strokeColor: '#4285f4',
                  strokeOpacity: 0.3,
                  strokeWeight: 1,
                  fillColor: '#4285f4',
                  fillOpacity: 0.12,
                  clickable: false,
                  draggable: false,
                  editable: false,
                  zIndex: 99,
                }}
              />
              {/* Pulsing blue dot for user location */}
              <OverlayView position={userLocation} mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}>
                <div className="pulse-dot" />
              </OverlayView>
              {/* Geofence circle */}
              <Circle
                center={userLocation}
                radius={radius}
                options={{
                  strokeColor: '#3366FF',
                  strokeOpacity: 0.7,
                  strokeWeight: 2,
                  fillColor: '#3366FF',
                  fillOpacity: 0.1,
                  draggable: false,
                  editable: false,
                }}
                onLoad={handleCircleLoad}
              />
              {/* Clustered custom markers for users inside radius */}
              <MarkerClusterer 
                options={{
                  styles: [
                    {
                      textColor: '#fff',
                      url: '/icons/cluster-icon.svg',
                      width: 40,
                      height: 40,
                      textSize: 14,
                    },
                  ],
                }}
              >
                {(clusterer) => (
                  <>
                    {filteredNearbyUsers.map(user => (
                      <Marker
                        key={user.user_id}
                        position={{ lat: user.lat, lng: user.lng }}
                        clusterer={clusterer}
                        onClick={() => setSelectedUser(user)}
                        icon={{
                          url: user.is_business ? '/icons/business-marker.svg' : '/icons/user-marker.svg',
                          scaledSize: new google.maps.Size(30, 30),
                        }}
                      />
                    ))}
                  </>
                )}
              </MarkerClusterer>
              {/* InfoWindow for selected user */}
              {selectedUser && (
                <InfoWindow
                  position={{ lat: selectedUser.lat, lng: selectedUser.lng }}
                  onCloseClick={() => setSelectedUser(null)}
                >
                  <div className="p-2 min-w-[180px]">
                    <div className="flex items-center gap-2">
                      <img src={selectedUser.avatar_url || '/icons/user-avatar.svg'} alt="avatar" className="w-8 h-8 rounded-full" />
                      <div>
                        <div className="font-bold">{selectedUser.full_name}</div>
                        <div className="text-xs text-muted-foreground">@{selectedUser.username}</div>
                      </div>
                    </div>
                    <div className="mt-2 text-xs">{selectedUser.bio}</div>
                    <div className="mt-2 flex gap-1 flex-wrap">
                      {(selectedUser.interests || []).map(i => (
                        <span key={i} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs">{i}</span>
                      ))}
                    </div>
                    <div className="mt-2 flex gap-2">
                      <Button size="sm" onClick={() => sendConnectionRequest(selectedUser.user_id)}>Connect</Button>
                      <Button size="sm" variant="outline" onClick={() => handleMessage(selectedUser.user_id)}>Message</Button>
                      <Button size="sm" variant="ghost" onClick={() => navigate('/profile')}>Profile</Button>
                    </div>
                  </div>
                </InfoWindow>
              )}
            </GoogleMap>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12">
            <MapPin className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">Unable to load map. Please enable location services and refresh.</p>
          </div>
        )}

        {loadingPosts ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-3">
                    <div className="h-4 bg-muted rounded w-1/3"></div>
                    <div className="h-4 bg-muted rounded w-full"></div>
                    <div className="h-4 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : posts.length === 0 ? (
          <Card>
            <CardContent className="card-no-posts">
              <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No posts nearby</h3>
              <p className="text-muted-foreground mb-4">
                Be the first to share something in your area!
              </p>
              <Button onClick={() => navigate('/profile')}>
                Create Your First Post
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {posts.map((post) => (
              <Card key={post.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-semibold text-primary">
                          {post.profiles?.full_name?.charAt(0) || 'U'}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium">{post.profiles?.full_name || 'Unknown User'}</p>
                        <p className="text-sm text-muted-foreground">@{post.profiles?.username || 'user'}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getPostTypeColor(post.post_type)}>
                        {post.post_type}
                      </Badge>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatTimeAgo(post.created_at)}
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  <p className="text-foreground mb-4">{post.content}</p>

                  {post.business_name && (
                    <div className="mb-3">
                      <Badge variant="outline" className="text-xs">
                        📍 {post.business_name}
                      </Badge>
                    </div>
                  )}

                  {post.event_date && post.event_location && (
                    <div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-sm font-medium text-blue-900">📅 Event Details</p>
                      <p className="text-sm text-blue-700">
                        {new Date(post.event_date).toLocaleDateString()} at {post.event_location}
                      </p>
                    </div>
                  )}

                  {post.deal_expires_at && (
                    <div className="mb-3 p-3 bg-green-50 rounded-lg border border-green-200">
                      <p className="text-sm font-medium text-green-900">💰 Deal expires</p>
                      <p className="text-sm text-green-700">
                        {new Date(post.deal_expires_at).toLocaleDateString()}
                      </p>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-3 border-t border-border">
                    <div className="flex items-center space-x-4">
                      <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-red-500">
                        <Heart className="h-4 w-4 mr-1" />
                        {post.likes_count || 0}
                      </Button>
                      <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-blue-500">
                        <MessageCircle className="h-4 w-4 mr-1" />
                        {post.comments_count || 0}
                      </Button>
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <MapPin className="h-3 w-3 mr-1" />
                      Nearby
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Debug Information - Remove this in production */}
        <div className="container mx-auto px-4">
          <DebugLocationInfo />
        </div>
      </main>

      <MobileNavigation />
    </div>
  );
};

export default Nearby;