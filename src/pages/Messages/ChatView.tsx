import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface Profile {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  interests?: string[];
  is_visible?: boolean;
  is_business?: boolean;
}

interface MessageRow {
  id: string;
  sender_id: string;
  receiver_id: string;
  content: string;
  created_at: string;
  is_read: boolean | null;
}

import { useAuth } from '@/hooks/useAuth';

const ChatView = () => {
  const [params] = useSearchParams();
  const conversationId = params.get('c');
  const partnerIdParam = params.get('u');
  const [messages, setMessages] = useState<MessageRow[]>([]);
  const [input, setInput] = useState('');
  const [sending, setSending] = useState(false);
  const [receiverId, setReceiverId] = useState<string>('');
  const [receiverName, setReceiverName] = useState<string>('');
  const [receiverProfile, setReceiverProfile] = useState<Profile | null>(null);
  const [showProfile, setShowProfile] = useState(false);
  const [warning, setWarning] = useState('');
  const listRef = useRef<HTMLDivElement | null>(null);
  const { user } = useAuth();

  // Initialize receiver from URL param if provided
  useEffect(() => {
    if (partnerIdParam) {
      setReceiverId(partnerIdParam);
    }
  }, [partnerIdParam]);

  // Fetch receiver profile when we have receiverId
  useEffect(() => {
    if (!receiverId) return;
    (async () => {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('id, full_name, username, avatar_url, bio, interests, is_business')
        .eq('user_id', receiverId)
        .single();
      if (error) {
        console.error('Error fetching receiver profile:', error);
        setReceiverName('Unknown');
        setReceiverProfile(null);
      } else {
        setReceiverName(profile.full_name || 'Unknown');
        setReceiverProfile(profile);
      }
    })();
  }, [receiverId]);

  const scrollToBottom = () => {
    listRef.current?.scrollTo({ top: listRef.current.scrollHeight, behavior: 'smooth' });
  };

  useEffect(() => {
    if (!conversationId) return;
    let ignore = false;

    (async () => {
      const result = await supabase
        .from('messages')
        .select('id,sender_id,receiver_id,content,created_at,is_read')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });
      const { data } = result as { data: MessageRow[] };
      if (!ignore && data) {
        setMessages(data);
        // Derive receiver from messages if not provided
        if (!receiverId && user?.id && data.length > 0) {
          const last = data[data.length - 1];
          const other = last.sender_id !== user.id ? last.sender_id : last.receiver_id;
          if (other && other !== user.id) setReceiverId(other);
        }
      }
    })();

    const channel = supabase.channel(`conv:${conversationId}`).on(
      'postgres_changes',
      { event: 'INSERT', schema: 'public', table: 'messages', filter: `conversation_id=eq.${conversationId}` },
      (payload) => {
        setMessages((prev) => {
          const next = [...prev, payload.new as MessageRow];
          // Try to derive receiver if still unknown
          if (!receiverId && user?.id) {
            const last = next[next.length - 1];
            const other = last.sender_id !== user.id ? last.sender_id : last.receiver_id;
            if (other && other !== user.id) setReceiverId(other);
          }
          return next;
        });
        scrollToBottom();
      }
    ).subscribe();

    return () => {
      ignore = true;
      supabase.removeChannel(channel);
    };
  }, [conversationId, receiverId, user?.id]);

  // Fallback: fetch participants to determine receiver when not provided
  useEffect(() => {
    if (!conversationId || !!receiverId || !user?.id) return;
    (async () => {
      try {
        const { data, error } = await supabase
          .from('conversation_participants')
          .select('user_id')
          .eq('conversation_id', conversationId);
        if (error) {
          console.error('Error fetching participants:', error);
          return;
        }
        if (Array.isArray(data)) {
          const other = data.find((p: { user_id: string }) => p.user_id !== user.id);
          if (other?.user_id) setReceiverId(other.user_id);
        }
      } catch (e) {
        console.error('Error resolving receiver from participants', e);
      }
    })();
  }, [conversationId, receiverId, user?.id]);

  useEffect(() => {
    scrollToBottom();
  }, [messages.length]);

  const send = async () => {
    if (!conversationId || !input.trim() || !user?.id) return;
    setSending(true);

    // 🔍 DEBUGGING CODE - Add this section
    console.log('=== DEBUGGING AUTH CONTEXT ===');
    console.log('Frontend user.id:', user.id);
    console.log('Frontend user object:', user);
    console.log('Conversation ID:', conversationId);
    console.log('Receiver ID:', receiverId);
    console.log('Partner ID Param:', partnerIdParam);

    // Test what auth.uid() returns in the database context
    // NOTE: Removed supabase.rpc('auth.uid') because it's not a valid RPC function in your Supabase types.
    // If you need the current user's ID, use user.id from your auth context.
    // Test the RLS policy conditions manually
    try {
      const { data: participantCheck, error: participantError } = await supabase
        .from('conversation_participants')
        .select('user_id')
        .eq('conversation_id', conversationId)
        .eq('user_id', user.id);
      console.log('Participant check result:', participantCheck);
      console.log('Participant check error:', participantError);
    } catch (e) {
      console.log('Participant check failed:', e);
    }
    console.log('=== END DEBUGGING ===');
    // 🔍 END DEBUGGING CODE

    const doInsert = async (convId: string, receiverUserId?: string) => {
      const insert = {
        conversation_id: convId,
        sender_id: user.id,
        receiver_id: receiverUserId || receiverId || '',
        content: input.trim(),
        is_read: false
      };

      // 🔍 DEBUGGING CODE - Add this too
      console.log('About to insert message:', insert);
      // 🔍 END DEBUGGING CODE

      return supabase.from('messages').insert(insert);
    };

    const ensureConversation = async (partnerId: string) => {
      const { data, error } = await supabase.rpc('get_or_create_conversation', { partner_id: partnerId });
      if (error || !data || typeof data !== 'string' || data.length !== 36) return undefined;
      return data as string;
    };

    try {
      let convId = conversationId;
      const partner = partnerIdParam || receiverId;
      let { error } = await doInsert(convId, partner);

      // 🔍 DEBUGGING CODE - Add this
      console.log('First insert attempt error:', error);
      // 🔍 END DEBUGGING CODE

      if (error) {
        // Try to self-heal by ensuring a proper conversation exists with both participants
        if (partner) {
          const newConv = await ensureConversation(partner);
          if (newConv) {
            convId = newConv;
            const usp = new URLSearchParams(window.location.search);
            usp.set('c', convId);
            if (partnerIdParam) usp.set('u', partnerIdParam);
            window.history.replaceState({}, '', `${window.location.pathname}?${usp.toString()}`);
            ({ error } = await doInsert(convId, partner));

            // 🔍 DEBUGGING CODE - Add this
            console.log('Second insert attempt error:', error);
            // 🔍 END DEBUGGING CODE
          }
        }
      }

      if (error) throw error;
      setInput('');
    } catch (e: unknown) {
      console.error('Send failed', e);
      let msg = '';
      if (typeof e === 'object' && e !== null && 'message' in e && typeof (e as { message: unknown }).message === 'string') {
        msg = (e as { message: string }).message;
      }
      if (msg.includes('No accepted connection')) {
        setWarning('You can only message accepted connections.');
      } else if (msg.includes('row-level security')) {
        setWarning('Cannot send message due to permissions. Make sure you are connected.');
      } else {
        setWarning('Failed to send message.');
      }
    } finally {
      setSending(false);
    }
  };

  if (!conversationId) {
    return <div className="p-4 text-sm text-muted-foreground">No conversation selected.</div>;
  }

  // WhatsApp-style chat bubble classes
  const getBubbleClass = (msg: MessageRow) =>
    msg.sender_id === user?.id
      ? 'bg-green-500 text-white self-end rounded-br-none'
      : 'bg-white text-gray-900 self-start rounded-bl-none border';

  return (
    <div className="flex flex-col h-full bg-[#ece5dd]">
      {/* Header with receiver info */}
      <div className="flex items-center gap-3 px-4 py-3 bg-[#075e54] text-white shadow-md sticky top-0 z-10">
        {receiverProfile?.avatar_url ? (
          <img src={receiverProfile.avatar_url} alt="avatar" className="w-10 h-10 rounded-full border border-white" />
        ) : (
          <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center text-lg font-bold border border-white">
            {receiverName ? receiverName[0] : '?'}
          </div>
        )}
        <div className="flex-1">
          <div className="font-semibold text-base">{receiverName || 'Recipient'}</div>
          {/* Optionally show online status or last seen here */}
        </div>
        {receiverProfile && (
          <Button variant="outline" size="sm" className="text-[#075e54] bg-white border-none hover:bg-gray-100" onClick={() => setShowProfile(true)}>
            View Profile
          </Button>
        )}
      </div>

      {/* Warning for missing recipient */}
      {warning && (
        <div className="bg-yellow-100 text-yellow-800 px-4 py-2 text-sm text-center">{warning}</div>
      )}

      {/* Chat messages */}
      <div ref={listRef} className="flex-1 overflow-y-auto px-2 py-4 flex flex-col gap-2">
        {messages.map((m) => (
          <div
            key={m.id}
            className={`max-w-[70%] px-4 py-2 rounded-2xl shadow-sm ${getBubbleClass(m)}`}
            style={{ alignSelf: m.sender_id === user?.id ? 'flex-end' : 'flex-start' }}
          >
            {m.content}
          </div>
        ))}
      </div>

      {/* Message input */}
      <div className="p-3 border-t flex gap-2 bg-[#f7f7f7]">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Type a message"
          onKeyDown={(e) => e.key === 'Enter' && send()}
          className="flex-1 rounded-full px-4 py-2 border border-gray-300 bg-white focus:outline-none focus:ring-2 focus:ring-[#075e54]"
        />
        <Button
          onClick={send}
          disabled={sending || !input.trim()}
          className="rounded-full px-6 bg-[#25d366] text-white font-bold hover:bg-[#20b358] disabled:bg-gray-300"
        >
          Send
        </Button>
      </div>

      {/* Receiver profile modal */}
      {showProfile && receiverProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-xs relative">
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-800"
              onClick={() => setShowProfile(false)}
            >
              ×
            </button>
            <div className="flex flex-col items-center gap-2">
              {receiverProfile.avatar_url ? (
                <img src={receiverProfile.avatar_url} alt="avatar" className="w-20 h-20 rounded-full border" />
              ) : (
                <div className="w-20 h-20 rounded-full bg-gray-300 flex items-center justify-center text-3xl font-bold">
                  {receiverName ? receiverName[0] : '?'}
                </div>
              )}
              <div className="font-semibold text-lg mt-2">{receiverName}</div>
              {receiverProfile.username && <div className="text-gray-600 text-sm text-center">@{receiverProfile.username}</div>}
              {receiverProfile.bio && <div className="text-gray-600 text-sm text-center">{receiverProfile.bio}</div>}

              {receiverProfile.interests && receiverProfile.interests.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {receiverProfile.interests.map((interest, idx) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {interest}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatView;
