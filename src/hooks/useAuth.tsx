import { useState, useEffect, createContext, useContext } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string, username: string) => Promise<{ error: Error | null }>;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Create profile if user just signed up
        if (event === 'SIGNED_IN' && session?.user) {
          // Check if profile exists, create if not
          setTimeout(() => {
            checkAndCreateProfile(session.user);
          }, 0);
        }
      }
    );

    // THEN check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkAndCreateProfile = async (user: User) => {
    try {
      // Check if profile exists
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching profile:', fetchError);
        return; // Don't show error toast, let the database trigger handle it
      }

      if (existingProfile) {
        // Profile already exists, no need to create
        console.log('Profile already exists for user:', user.id);
        return;
      }

      // Only create profile if it doesn't exist and database trigger failed
      console.log('Creating profile manually for user:', user.id);
      console.log('User metadata:', user.user_metadata);

      // Extract name from Google OAuth metadata with better fallbacks
      const fullName = user.user_metadata?.full_name ||
                      user.user_metadata?.name ||
                      user.email?.split('@')[0] ||
                      'User';

      // Generate a username from email or metadata
      const username = user.user_metadata?.preferred_username ||
                      user.user_metadata?.username ||
                      user.email?.split('@')[0] ||
                      `user_${user.id.slice(0, 8)}`;

      const { error: createError } = await supabase
        .from('profiles')
        .insert({
          user_id: user.id,
          full_name: fullName,
          username: username,
          is_visible: true,
          visibility_radius: 5000
        });

      if (createError) {
        console.error('Error creating profile manually:', createError);
        // Don't show error toast for profile creation failures
        // The user can still use the app and create profile later
      } else {
        console.log('Profile created manually for user:', user.id);
      }
    } catch (err) {
      console.error('Unexpected error in checkAndCreateProfile:', err);
      // Don't show error toast, just log it
    }
  };

  const signUp = async (email: string, password: string, fullName: string, username: string) => {
    const redirectUrl = `${window.location.origin}/`;
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          full_name: fullName,
          username: username,
        }
      }
    });
    
    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    return { error };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  return (
    <AuthContext.Provider value={{ user, session, loading, signUp, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook to check if user needs location onboarding
export const useLocationOnboarding = () => {
  const [needsOnboarding, setNeedsOnboarding] = useState<boolean | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    const checkLocationStatus = async () => {
      if (!user) {
        setNeedsOnboarding(false);
        return;
      }

      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('location_lat, location_lng')
          .eq('user_id', user.id)
          .single();

        if (error) {
          console.error('Error checking location status:', error);
          setNeedsOnboarding(false);
          return;
        }

        // User needs onboarding if they don't have location set
        setNeedsOnboarding(!profile.location_lat || !profile.location_lng);
      } catch (error) {
        console.error('Error checking location status:', error);
        setNeedsOnboarding(false);
      }
    };

    checkLocationStatus();
  }, [user]);

  return needsOnboarding;
};