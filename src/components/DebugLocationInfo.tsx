import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, Users, Eye, EyeOff, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/hooks/use-toast';

interface DebugInfo {
  currentUser: {
    id: string;
    location_lat: number | null;
    location_lng: number | null;
    is_visible: boolean | null;
    visibility_radius: number | null;
    username: string;
  } | null;
  allUsers: Array<{
    id: string;
    username: string;
    location_lat: number | null;
    location_lng: number | null;
    is_visible: boolean | null;
    distance?: number;
  }>;
  nearbyUsers: Array<{
    id: string;
    username: string;
    distance_m?: number;
  }>;
}

const DebugLocationInfo = () => {
  const { user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    currentUser: null,
    allUsers: [],
    nearbyUsers: []
  });
  const [loading, setLoading] = useState(false);

  const fetchDebugInfo = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      // Get current user profile
      const { data: currentProfile } = await supabase
        .from('profiles')
        .select('id, user_id, username, location_lat, location_lng, is_visible, visibility_radius')
        .eq('user_id', user.id)
        .single();

      // Get all users with locations
      const { data: allProfiles } = await supabase
        .from('profiles')
        .select('id, username, location_lat, location_lng, is_visible')
        .not('location_lat', 'is', null)
        .not('location_lng', 'is', null);

      // Calculate distances if current user has location
      let usersWithDistance = allProfiles || [];
      if (currentProfile?.location_lat && currentProfile?.location_lng) {
        usersWithDistance = (allProfiles || []).map(profile => {
          if (profile.location_lat && profile.location_lng) {
            // Haversine formula
            const R = 6371; // Earth's radius in km
            const dLat = (profile.location_lat - currentProfile.location_lat) * Math.PI / 180;
            const dLng = (profile.location_lng - currentProfile.location_lng) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                     Math.cos(currentProfile.location_lat * Math.PI / 180) * 
                     Math.cos(profile.location_lat * Math.PI / 180) *
                     Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distance = R * c;
            
            return { ...profile, distance: Math.round(distance * 1000) / 1000 };
          }
          return profile;
        });
      }

      // Test the nearby users function
      let nearbyUsers = [];
      if (currentProfile?.location_lat && currentProfile?.location_lng) {
        const { data: nearby, error } = await supabase.rpc('find_nearby_users', {
          user_lat: currentProfile.location_lat,
          user_lng: currentProfile.location_lng,
          radius_m: 5000
        });
        
        if (error) {
          console.error('Error testing nearby function:', error);
          toast({
            title: 'Debug Error',
            description: 'Failed to test nearby users function: ' + error.message,
            variant: 'destructive'
          });
        } else {
          nearbyUsers = nearby || [];
        }
      }

      setDebugInfo({
        currentUser: currentProfile,
        allUsers: usersWithDistance,
        nearbyUsers
      });

    } catch (error) {
      console.error('Debug fetch error:', error);
      toast({
        title: 'Debug Error',
        description: 'Failed to fetch debug information',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, [user]);

  if (!user) return null;

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Location Debug Info
          </span>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchDebugInfo}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current User Info */}
        <div>
          <h4 className="font-semibold mb-2">Your Profile:</h4>
          {debugInfo.currentUser ? (
            <div className="bg-blue-50 p-3 rounded-lg space-y-1">
              <p><strong>Username:</strong> {debugInfo.currentUser.username}</p>
              <p><strong>Location:</strong> {
                debugInfo.currentUser.location_lat && debugInfo.currentUser.location_lng
                  ? `${debugInfo.currentUser.location_lat.toFixed(6)}, ${debugInfo.currentUser.location_lng.toFixed(6)}`
                  : 'Not set'
              }</p>
              <p className="flex items-center gap-2">
                <strong>Visible:</strong> 
                {debugInfo.currentUser.is_visible ? (
                  <Badge variant="default" className="flex items-center gap-1">
                    <Eye className="h-3 w-3" /> Visible
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <EyeOff className="h-3 w-3" /> Hidden
                  </Badge>
                )}
              </p>
              <p><strong>Radius:</strong> {debugInfo.currentUser.visibility_radius || 'Not set'}m</p>
            </div>
          ) : (
            <p className="text-muted-foreground">No profile found</p>
          )}
        </div>

        {/* All Users */}
        <div>
          <h4 className="font-semibold mb-2">All Users with Locations ({debugInfo.allUsers.length}):</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {debugInfo.allUsers.map((profile, idx) => (
              <div key={idx} className="bg-gray-50 p-2 rounded text-sm">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{profile.username}</span>
                  <div className="flex items-center gap-2">
                    {profile.distance && (
                      <Badge variant="outline">{profile.distance}km</Badge>
                    )}
                    {profile.is_visible ? (
                      <Eye className="h-3 w-3 text-green-600" />
                    ) : (
                      <EyeOff className="h-3 w-3 text-gray-400" />
                    )}
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  {profile.location_lat?.toFixed(4)}, {profile.location_lng?.toFixed(4)}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Nearby Users Function Result */}
        <div>
          <h4 className="font-semibold mb-2 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Nearby Users Function Result ({debugInfo.nearbyUsers.length}):
          </h4>
          {debugInfo.nearbyUsers.length > 0 ? (
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {debugInfo.nearbyUsers.map((user, idx) => (
                <div key={idx} className="bg-green-50 p-2 rounded text-sm">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{user.username}</span>
                    <Badge variant="default">{user.distance_m ? (user.distance_m / 1000).toFixed(2) : ''}km</Badge>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-sm">No nearby users found by function</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default DebugLocationInfo;
